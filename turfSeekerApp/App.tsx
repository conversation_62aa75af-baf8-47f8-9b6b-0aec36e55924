import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import './global.css';

// Import screens
import SplashScreen from './screens/welcome/SplashScreen';
import WelcomeScreen from './screens/welcome/WelcomeScreen';
import FirstScreen from './screens/Walkthorugh/FirstScreen';
import SecondScreen from './screens/Walkthorugh/SecondScreen';
import ThirdScreen from './screens/Walkthorugh/ThirdScreen';
import FourthScreen from './screens/Walkthorugh/FourthScreen';
import GetStartedScreen from './screens/getStart/GetStartedScreen';
import LoginScreen from './screens/auth/LoginScreen';
import RegisterScreen from './screens/auth/RegisterScreen';
import ForgotPasswordScreen from './screens/auth/ForgotPasswordScreen';
import OtpVerificationScreen from './screens/auth/OtpVerificationScreen';
import ResetPasswordScreen from './screens/auth/ResetPasswordScreen';
import LocationScreen from './screens/onboarding/LocationScreen';
import LocationSearchScreen from './screens/onboarding/LocationSearchScreen';
import MapScreen from './screens/map/MapScreen';

const Stack = createNativeStackNavigator();

export default function App() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <NavigationContainer>
        <StatusBar style="auto" />
        <Stack.Navigator
          initialRouteName="Splash"
          screenOptions={{
            headerShown: false,
            animation: 'fade',
          }}
        >
          <Stack.Screen name="Splash" component={SplashScreen} />
          <Stack.Screen name="Welcome" component={WelcomeScreen} />
          <Stack.Screen name="FirstScreen" component={FirstScreen} />
          <Stack.Screen name="SecondScreen" component={SecondScreen} />
          <Stack.Screen name="ThirdScreen" component={ThirdScreen} />
          <Stack.Screen name="FourthScreen" component={FourthScreen} />
          <Stack.Screen name="GetStarted" component={GetStartedScreen} />
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
          <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
          <Stack.Screen name="OtpVerification" component={OtpVerificationScreen} />
          <Stack.Screen name="ResetPassword" component={ResetPasswordScreen} />
          <Stack.Screen name="Location" component={LocationScreen} />
          <Stack.Screen name="LocationSearch" component={LocationSearchScreen} />
          <Stack.Screen name="Map" component={MapScreen} />
        </Stack.Navigator>
      </NavigationContainer>
    </GestureHandlerRootView>
  );
}