{"expo": {"name": "my-expo-app", "slug": "my-expo-app", "version": "1.0.0", "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "experiments": {"tsconfigPaths": true}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow Turf Seeker to use your location."}]], "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Turf Seeker needs your location to show nearby turfs.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Turf Seeker needs your location to show nearby turfs."}, "bundleIdentifier": "com.turfseeker.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION"], "package": "com.turfseeker.app"}}}