import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, NavigationProp, ParamListBase } from '@react-navigation/native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import * as Location from 'expo-location';

const { width, height } = Dimensions.get('window');

const LocationScreen = () => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const [locationPermissionStatus, setLocationPermissionStatus] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleUseCurrentLocation = async () => {
    console.log('Requesting location permission...');
    setIsLoading(true);

    try {
      // Request foreground location permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      setLocationPermissionStatus(status);

      if (status !== 'granted') {
        setIsLoading(false);
        Alert.alert(
          'Permission Denied',
          'Permission to access location was denied. Please enable location services to use this feature.',
          [
            { text: 'OK' }
          ]
        );
        return;
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      setIsLoading(false);
      console.log('Location obtained:', location);

      // Navigate to the location search screen with the current location
      navigation.navigate('LocationSearch', {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        locationName: 'Current Location',
        isManualEntry: false
      });

    } catch (error) {
      setIsLoading(false);
      console.error('Error getting location:', error);
      Alert.alert(
        'Error',
        'There was an error getting your location. Please try again.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleEnterManually = () => {
    // Navigate to the location search screen with isManualEntry flag
    console.log('Enter location manually');
    navigation.navigate('LocationSearch', {
      isManualEntry: true
    });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      <Animated.View
        style={styles.contentContainer}
        entering={FadeIn.duration(500)}
      >
        {/* Header Text */}
        <Text style={styles.headerText}>What's your location?</Text>
        <Text style={styles.subtitle}>
          We need your location to show available and nearby turfs
        </Text>

        {/* Map Image */}
        <View style={styles.mapContainer}>
          <Image
            source={require('../../assets/globe.png')}
            style={styles.mapImage}
            resizeMode="contain"
          />
        </View>

        {/* Use Current Location Button */}
        <TouchableOpacity
          style={styles.locationButton}
          onPress={handleUseCurrentLocation}
          activeOpacity={0.8}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="#FFFFFF" style={styles.buttonIcon} />
          ) : (
            <Feather name="map-pin" size={20} color="#FFFFFF" style={styles.buttonIcon} />
          )}
          <Text style={styles.locationButtonText}>
            {isLoading ? 'Getting location...' : 'Use my current location'}
          </Text>
        </TouchableOpacity>

        {/* Permission Status Indicator (only shown after permission request) */}
        {locationPermissionStatus && (
          <Text style={[
            styles.permissionStatus,
            locationPermissionStatus === 'granted' ? styles.permissionGranted : styles.permissionDenied
          ]}>
            {locationPermissionStatus === 'granted'
              ? 'Location permission granted'
              : 'Location permission denied'}
          </Text>
        )}

        {/* Enter Location Manually */}
        <TouchableOpacity
          onPress={handleEnterManually}
          disabled={isLoading}
        >
          <Text style={[
            styles.manualEntryText,
            isLoading && styles.disabledText
          ]}>Enter location manually</Text>
        </TouchableOpacity>

        {/* Bottom Indicator */}
        <View style={styles.bottomIndicator} />
      </Animated.View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  contentContainer: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 40,
    textAlign: 'left',

  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginTop: 8,
    marginBottom: 40,
    textAlign: 'center',
  },
  mapContainer: {
    width: width * 0.8,
    height: width * 0.8,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  locationButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#00A693',
    borderRadius: 25,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonIcon: {
    marginRight: 10,
  },
  locationButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  permissionStatus: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
    marginBottom: 15,
    paddingHorizontal: 20,
    paddingVertical: 5,
    borderRadius: 15,
  },
  permissionGranted: {
    backgroundColor: '#E6F7F4',
    color: '#00A693',
  },
  permissionDenied: {
    backgroundColor: '#FFEBEE',
    color: '#F44336',
  },
  manualEntryText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    textDecorationLine: 'underline',
    marginBottom: 40,
  },
  disabledText: {
    color: '#AAAAAA',
    opacity: 0.7,
  },
  bottomIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#D9D9D9',
    borderRadius: 2,
    position: 'absolute',
    bottom: 20,
  },
});

export default LocationScreen;
