import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  Keyboard,
  Image,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, NavigationProp, RouteProp, ParamListBase } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import * as Location from 'expo-location';
import Animated, { FadeIn } from 'react-native-reanimated';

const { width } = Dimensions.get('window');

// Sample turf data structure - in a real app, this would come from an API
// We'll use this for typing purposes
type TurfData = {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  postalCode?: string;
  distance?: number;
  rating?: number;
};

type RouteParams = {
  latitude?: number;
  longitude?: number;
  locationName?: string;
  isManualEntry?: boolean;
};

const LocationSearchScreen = () => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const params = route.params || {};

  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [turfs, setTurfs] = useState<TurfData[]>([]);
  const [filteredTurfs, setFilteredTurfs] = useState<TurfData[]>([]);
  const [currentLocation, setCurrentLocation] = useState<{
    latitude: number;
    longitude: number;
    locationName: string;
  } | null>(null);

  const searchInputRef = useRef<TextInput>(null);

  useEffect(() => {
    // If we have location from params, use it
    if (params.latitude && params.longitude) {
      const locationName = params.locationName || 'Current Location';

      setCurrentLocation({
        latitude: params.latitude,
        longitude: params.longitude,
        locationName: locationName,
      });

      // Always set the search query to the location name
      setSearchQuery(locationName);

      // In a real app, you would fetch turfs near this location from an API
      // For now, we'll just show an empty state until the user searches
      setFilteredTurfs([]);
    }

    // If manual entry is requested, focus the search input
    if (params.isManualEntry) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 500);
    }
  }, [params]);

  const handleSearch = (text: string) => {
    setSearchQuery(text);

    // If empty search, default to Dhaka
    if (text.trim() === '') {
      text = 'Dhaka';
      setSearchQuery(text);
    }

    // In a real app, you would make an API call to search for turfs
    // For now, we'll generate more realistic turf data based on the search query
    setIsLoading(true);

    // Simulate API call delay
    setTimeout(() => {
      // Generate dynamic turf data based on the search query
      const searchLocation = text.trim();

      // Create more realistic turf names based on the location
      let generatedTurfs: TurfData[] = [];

      // If searching for Bashundhara, use real turf names
      if (searchLocation.toLowerCase().includes('bashundhara')) {
        generatedTurfs = [
          {
            id: '1',
            name: 'Bashundhara Sports Complex',
            address: 'Block G, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 1.2,
            rating: 4.7,
          },
          {
            id: '2',
            name: 'Bashundhara Kings Arena',
            address: 'Block D, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 1.8,
            rating: 4.5,
          },
          {
            id: '3',
            name: 'Bashundhara Football Turf',
            address: 'Block C, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 2.3,
            rating: 4.8,
          }
        ];
      }
      // If searching for Uttara, use real turf names
      else if (searchLocation.toLowerCase().includes('uttara')) {
        generatedTurfs = [
          {
            id: '1',
            name: 'Uttara Football Turf',
            address: 'Sector 10, Uttara',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1230',
            distance: 0.8,
            rating: 4.6,
          },
          {
            id: '2',
            name: 'Uttara Sports Club',
            address: 'Sector 7, Uttara',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1230',
            distance: 1.5,
            rating: 4.3,
          }
        ];
      }
      // If searching for Gulshan, use real turf names
      else if (searchLocation.toLowerCase().includes('gulshan')) {
        generatedTurfs = [
          {
            id: '1',
            name: 'Gulshan Youth Club',
            address: 'Gulshan Avenue, Gulshan 1',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1212',
            distance: 1.1,
            rating: 4.5,
          },
          {
            id: '2',
            name: 'Gulshan Sports Complex',
            address: 'Road 103, Gulshan 2',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1212',
            distance: 1.9,
            rating: 4.7,
          }
        ];
      }
      // For other locations, generate generic turf names
      else {
        const numberOfTurfs = Math.floor(Math.random() * 3) + 2; // Random number between 2-4

        for (let i = 0; i < numberOfTurfs; i++) {
          // Generate turf data based on the search location
          generatedTurfs.push({
            id: `${i + 1}`,
            name: `${searchLocation} Football Turf ${i + 1}`,
            address: `Area ${i + 1}, ${searchLocation}`,
            city: searchLocation,
            country: 'Bangladesh',
            postalCode: `${Math.floor(Math.random() * 900000) + 100000}`,
            distance: parseFloat((Math.random() * 5 + 0.5).toFixed(1)), // Random distance between 0.5-5.5 km
            rating: parseFloat((Math.random() * 1 + 4).toFixed(1)), // Random rating between 4.0-5.0
          });
        }
      }

      console.log(`Found ${generatedTurfs.length} turfs in ${searchLocation}`);
      setTurfs(generatedTurfs);
      setFilteredTurfs(generatedTurfs);

      // Update current location to reflect the search
      // For Bashundhara, use actual coordinates
      if (searchLocation.toLowerCase().includes('bashundhara')) {
        setCurrentLocation({
          latitude: 23.8203,
          longitude: 90.4360,
          locationName: searchLocation,
        });
      }
      // For Uttara, use actual coordinates
      else if (searchLocation.toLowerCase().includes('uttara')) {
        setCurrentLocation({
          latitude: 23.8759,
          longitude: 90.3795,
          locationName: searchLocation,
        });
      }
      // For Gulshan, use actual coordinates
      else if (searchLocation.toLowerCase().includes('gulshan')) {
        setCurrentLocation({
          latitude: 23.7931,
          longitude: 90.4137,
          locationName: searchLocation,
        });
      }
      // For other locations, use default Dhaka coordinates
      else {
        setCurrentLocation({
          latitude: 23.8103,
          longitude: 90.4125,
          locationName: searchLocation,
        });
      }

      setIsLoading(false);
    }, 1500); // Simulate network delay
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setTurfs([]);
    setFilteredTurfs([]);
    Keyboard.dismiss();
  };

  const handleSearchButtonPress = () => {
    // Always search, even if empty (will default to Dhaka)
    handleSearch(searchQuery);
    Keyboard.dismiss();
  };

  const handleUseCurrentLocation = async () => {
    setIsLoading(true);

    try {
      // Check if we already have permission
      const { status } = await Location.getForegroundPermissionsAsync();

      if (status !== 'granted') {
        // Request permission if we don't have it
        const { status: newStatus } = await Location.requestForegroundPermissionsAsync();

        if (newStatus !== 'granted') {
          setIsLoading(false);
          return;
        }
      }

      // Get current location
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      // Get the actual location name using reverse geocoding
      try {
        const reverseGeocode = await Location.reverseGeocodeAsync({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });

        let locationName = 'Current Location';

        if (reverseGeocode && reverseGeocode.length > 0) {
          const address = reverseGeocode[0];
          // Create a readable location name from the address components
          const components = [
            address.name,
            address.street,
            address.district,
            address.city,
            address.region,
          ].filter((component): component is string => Boolean(component)); // Remove any undefined or empty values

          if (components.length > 0) {
            // Use the most specific component available
            locationName = components[0];

            // If we have a city or district, use that instead
            if (address.city) locationName = address.city;
            else if (address.district) locationName = address.district;
            else if (address.region) locationName = address.region;
          }
        }

        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          locationName: locationName,
        });

        // Set the search query to the actual location name
        setSearchQuery(locationName);
        console.log('Location name set to:', locationName);
      } catch (geocodeError) {
        console.error('Error getting location name:', geocodeError);
        // Fallback to generic name if reverse geocoding fails
        const locationName = 'Current Location';
        setCurrentLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
          locationName: locationName,
        });
        setSearchQuery(locationName);
      }

      // Get the location name from the current context
      const currentLocationName = currentLocation?.locationName || 'Current Location';

      // In a real app, you would fetch turfs near this location from an API
      // For now, we'll generate more realistic turf data based on the location

      // Extract city name
      const cityName = currentLocationName.split(',')[0].trim();

      // Generate turfs based on approximate location
      let generatedTurfs: TurfData[] = [];

      // Check if we're near Bashundhara based on coordinates
      const isNearBashundhara =
        location.coords.latitude > 23.81 && location.coords.latitude < 23.83 &&
        location.coords.longitude > 90.43 && location.coords.longitude < 90.45;

      // Check if we're near Uttara based on coordinates
      const isNearUttara =
        location.coords.latitude > 23.86 && location.coords.latitude < 23.89 &&
        location.coords.longitude > 90.37 && location.coords.longitude < 90.39;

      // Check if we're near Gulshan based on coordinates
      const isNearGulshan =
        location.coords.latitude > 23.78 && location.coords.latitude < 23.80 &&
        location.coords.longitude > 90.41 && location.coords.longitude < 90.43;

      if (isNearBashundhara) {
        // Show Bashundhara turfs
        generatedTurfs = [
          {
            id: '1',
            name: 'Bashundhara Sports Complex',
            address: 'Block G, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 0.7,
            rating: 4.7,
          },
          {
            id: '2',
            name: 'Bashundhara Kings Arena',
            address: 'Block D, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 1.2,
            rating: 4.5,
          },
          {
            id: '3',
            name: 'Bashundhara Football Turf',
            address: 'Block C, Bashundhara R/A',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1229',
            distance: 1.5,
            rating: 4.8,
          }
        ];
      } else if (isNearUttara) {
        // Show Uttara turfs
        generatedTurfs = [
          {
            id: '1',
            name: 'Uttara Football Turf',
            address: 'Sector 10, Uttara',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1230',
            distance: 0.5,
            rating: 4.6,
          },
          {
            id: '2',
            name: 'Uttara Sports Club',
            address: 'Sector 7, Uttara',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1230',
            distance: 0.9,
            rating: 4.3,
          }
        ];
      } else if (isNearGulshan) {
        // Show Gulshan turfs
        generatedTurfs = [
          {
            id: '1',
            name: 'Gulshan Youth Club',
            address: 'Gulshan Avenue, Gulshan 1',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1212',
            distance: 0.6,
            rating: 4.5,
          },
          {
            id: '2',
            name: 'Gulshan Sports Complex',
            address: 'Road 103, Gulshan 2',
            city: 'Dhaka',
            country: 'Bangladesh',
            postalCode: '1212',
            distance: 1.1,
            rating: 4.7,
          }
        ];
      } else {
        // For other locations, generate generic turf names based on the nearest area
        const numberOfTurfs = Math.floor(Math.random() * 3) + 2; // Random number between 2-4

        for (let i = 0; i < numberOfTurfs; i++) {
          // Generate turf data based on the current location
          generatedTurfs.push({
            id: `${i + 1}`,
            name: `${cityName} Football Turf ${i + 1}`,
            address: `Area ${i + 1}, ${cityName}`,
            city: cityName,
            country: 'Bangladesh',
            postalCode: `${Math.floor(Math.random() * 900000) + 100000}`,
            distance: parseFloat((Math.random() * 2 + 0.3).toFixed(1)), // Random distance between 0.3-2.3 km
            rating: parseFloat((Math.random() * 1 + 4).toFixed(1)), // Random rating between 4.0-5.0
          });
        }
      }

      console.log(`Found ${generatedTurfs.length} turfs near ${cityName}`);
      setTurfs(generatedTurfs);
      setFilteredTurfs(generatedTurfs);

    } catch (error) {
      console.error('Error getting location:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  const renderTurfItem = ({ item }: { item: TurfData }) => (
    <TouchableOpacity
      style={styles.turfItem}
      activeOpacity={0.7}
      onPress={() => {
        // Navigate to the map screen with the selected turf
        console.log('Selected turf:', item.name);

        // Add random latitude and longitude for the turf (since our data is mock)
        // In a real app, these would come from the API
        const turfWithLocation = {
          ...item,
          latitude: currentLocation?.latitude
            ? currentLocation.latitude + (Math.random() - 0.5) * 0.01
            : 23.8103 + (Math.random() - 0.5) * 0.01, // Default to Dhaka with small offset
          longitude: currentLocation?.longitude
            ? currentLocation.longitude + (Math.random() - 0.5) * 0.01
            : 90.4125 + (Math.random() - 0.5) * 0.01, // Default to Dhaka with small offset
        };

        // Navigate to the map screen
        navigation.navigate('Map', {
          turf: turfWithLocation,
          currentLocation: currentLocation,
        });
      }}
    >
      <View style={styles.turfItemContent}>
        <Feather name="map-pin" size={20} color="#333" style={styles.turfIcon} />
        <View style={styles.turfInfo}>
          <Text style={styles.turfName}>{item.name}</Text>
          <Text style={styles.turfAddress}>{item.address}</Text>
          <Text style={styles.turfCity}>{item.city}, {item.country} {item.postalCode}</Text>

          {/* Additional turf details */}
          <View style={styles.turfDetails}>
            {item.distance && (
              <View style={styles.turfDetailItem}>
                <Feather name="navigation" size={14} color="#00A693" />
                <Text style={styles.turfDetailText}>{item.distance} km</Text>
              </View>
            )}
            {item.rating && (
              <View style={styles.turfDetailItem}>
                <Feather name="star" size={14} color="#00A693" />
                <Text style={styles.turfDetailText}>{item.rating}</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Feather name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Enter your area or location name</Text>
      </View>

      {/* Search Input */}
      <View style={styles.searchContainer}>
        <Feather name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          ref={searchInputRef}
          style={styles.searchInput}
          placeholder="Enter location (e.g., Dhaka, Chittagong)"
          value={searchQuery}
          onChangeText={setSearchQuery}
          onSubmitEditing={handleSearchButtonPress}
          returnKeyType="search"
          autoCapitalize="none"
          autoCorrect={false}
        />
        {searchQuery.length > 0 ? (
          <>
            <TouchableOpacity
              onPress={handleSearchButtonPress}
              style={styles.searchButton}
            >
              <Feather name="search" size={20} color="#FFFFFF" />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleClearSearch} style={styles.clearButton}>
              <Feather name="x" size={20} color="#666" />
            </TouchableOpacity>
          </>
        ) : (
          <TouchableOpacity
            onPress={handleSearchButtonPress}
            style={styles.searchButton}
          >
            <Feather name="search" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        )}
      </View>

      {/* Use Current Location Button */}
      <TouchableOpacity
        style={styles.currentLocationButton}
        onPress={handleUseCurrentLocation}
        disabled={isLoading}
      >
        <Feather name="map-pin" size={20} color="#00A693" style={styles.locationIcon} />
        <Text style={styles.currentLocationText}>Use my current location</Text>
        <Feather name="chevron-right" size={20} color="#00A693" style={styles.arrowIcon} />
      </TouchableOpacity>

      {/* Search Results Header */}
      <View style={styles.resultsHeader}>
        <Text style={styles.resultsTitle}>SEARCH RESULTS</Text>
      </View>

      {/* Loading Indicator */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00A693" />
          <Text style={styles.loadingText}>Finding turfs near you...</Text>
        </View>
      ) : (
        <>
          {/* Turf List */}
          {filteredTurfs.length > 0 ? (
            <FlatList
              data={filteredTurfs}
              renderItem={renderTurfItem}
              keyExtractor={(item) => item.id}
              contentContainerStyle={styles.turfList}
              showsVerticalScrollIndicator={false}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Image
                source={require('../../assets/globe.png')}
                style={styles.emptyImage}
                resizeMode="contain"
              />
              <Text style={styles.emptyText}>Enter your area to find turfs</Text>
              <Text style={styles.emptySubtext}>
                Try searching for "Dhaka", "Chittagong", or any other location in Bangladesh
              </Text>
            </View>
          )}
        </>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: 35,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    gap: 8,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    marginVertical: 12,
    paddingHorizontal: 12,
    height: 48,
    borderWidth: 1,
    borderColor: '#EEEEEE',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: '100%',
    fontSize: 16,
    color: '#333333',
  },
  clearButton: {
    padding: 8,
  },
  searchButton: {
    backgroundColor: '#00A693',
    padding: 8,
    borderRadius: 8,
    marginRight: 8,
  },
  currentLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  locationIcon: {
    marginRight: 12,
  },
  currentLocationText: {
    flex: 1,
    fontSize: 16,
    color: '#00A693',
    fontWeight: '500',
  },
  arrowIcon: {
    marginLeft: 8,
  },
  resultsHeader: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#F5F5F5',
  },
  resultsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  turfList: {
    paddingBottom: 24,
  },
  turfItem: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
  },
  turfItemContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  turfIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  turfInfo: {
    flex: 1,
  },
  turfName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 4,
  },
  turfAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 2,
  },
  turfCity: {
    fontSize: 14,
    color: '#666666',
  },
  turfDetails: {
    flexDirection: 'row',
    marginTop: 8,
  },
  turfDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  turfDetailText: {
    fontSize: 14,
    color: '#00A693',
    fontWeight: '500',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyImage: {
    width: width * 0.5,
    height: width * 0.5,
    marginBottom: 24,
    opacity: 0.5,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    paddingHorizontal: 20,
    marginTop: 8,
  },
});

export default LocationSearchScreen;
