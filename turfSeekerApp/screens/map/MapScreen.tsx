import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
  ActivityIndicator,
  Alert,
  Image,
} from 'react-native';
import { useNavigation, useRoute, NavigationProp, RouteProp, ParamListBase } from '@react-navigation/native';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { WebView } from 'react-native-webview';

const { width } = Dimensions.get('window');

// Define the route params type
type RouteParams = {
  turf: {
    id: string;
    name: string;
    address: string;
    city: string;
    country: string;
    postalCode?: string;
    distance?: number;
    rating?: number;
    latitude?: number;
    longitude?: number;
  };
  currentLocation?: {
    latitude: number;
    longitude: number;
    locationName: string;
  };
};

const MapScreen = () => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const route = useRoute<RouteProp<Record<string, RouteParams>, string>>();
  const { turf, currentLocation } = route.params || {};

  const [isLoading, setIsLoading] = useState(true);
  const [mapUrl, setMapUrl] = useState('');

  useEffect(() => {
    // Set up the map with turf location
    if (turf?.latitude && turf?.longitude) {
      generateMapUrl(turf.latitude, turf.longitude, turf.name);
      setIsLoading(false);
    } else if (currentLocation?.latitude && currentLocation?.longitude) {
      // If we have current location but no turf coordinates, use current location
      // and add a small offset to simulate turf location
      const offset = 0.001; // Small offset for demonstration
      const turfLat = currentLocation.latitude + offset;
      const turfLng = currentLocation.longitude + offset;

      generateMapUrl(turfLat, turfLng, turf?.name || 'Turf Location');
      setIsLoading(false);
    } else {
      // If no location data is available, try to get current location
      getCurrentLocation();
    }
  }, [turf, currentLocation]);

  const generateMapUrl = (latitude: number, longitude: number, label: string) => {
    // Create a Google Maps URL that can be loaded in WebView with search for turfs
    let searchTerm;

    // Check if this is a specific turf name or a location
    if (label.includes('Sports Complex') ||
        label.includes('Football Turf') ||
        label.includes('Arena') ||
        label.includes('Club')) {
      // This is a specific turf name, search for it directly
      searchTerm = label;
    } else if (label.includes('Bashundhara')) {
      // For Bashundhara, search for football turfs in Bashundhara
      searchTerm = 'football turfs in Bashundhara Dhaka';
    } else if (label.includes('Uttara')) {
      // For Uttara, search for football turfs in Uttara
      searchTerm = 'football turfs in Uttara Dhaka';
    } else if (label.includes('Gulshan')) {
      // For Gulshan, search for football turfs in Gulshan
      searchTerm = 'football turfs in Gulshan Dhaka';
    } else {
      // For other locations, search for turfs near the location
      searchTerm = `football turfs near ${label}`;
    }

    // Create the map URL with the search term
    const url = `https://www.google.com/maps/embed/v1/search?key=AIzaSyDXxOwT2__QUkdBZsdhIyLd8tewSLhxc3s&q=${encodeURIComponent(searchTerm)}&center=${latitude},${longitude}&zoom=14`;
    setMapUrl(url);
  };

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert(
          'Permission Denied',
          'Location permission is required to show the map properly.',
          [{ text: 'OK' }]
        );
        // Use default location (Dhaka)
        generateMapUrl(23.8103, 90.4125, 'Dhaka');
        setIsLoading(false);
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const turfLat = location.coords.latitude + 0.001; // Small offset
      const turfLng = location.coords.longitude + 0.001; // Small offset

      generateMapUrl(turfLat, turfLng, turf?.name || 'Turf Location');
      setIsLoading(false);
    } catch (error) {
      console.error('Error getting location:', error);
      // Use default location (Dhaka)
      generateMapUrl(23.8103, 90.4125, 'Dhaka');
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Feather name="arrow-left" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{turf?.name || 'Turf Location'}</Text>
      </View>

      {/* Map View */}
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#00A693" />
          <Text style={styles.loadingText}>Loading map...</Text>
        </View>
      ) : (
        <View style={styles.mapContainer}>
          {mapUrl ? (
            <WebView
              style={styles.map}
              source={{ uri: mapUrl }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              renderLoading={() => (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#00A693" />
                </View>
              )}
            />
          ) : (
            <View style={styles.fallbackContainer}>
              <Image
                source={require('../../assets/globe.png')}
                style={styles.fallbackImage}
                resizeMode="contain"
              />
              <Text style={styles.fallbackText}>Map could not be loaded</Text>
            </View>
          )}
        </View>
      )}

      {/* Turf Info Card */}
      <View style={styles.turfInfoCard}>
        <Text style={styles.turfName}>{turf?.name || 'Turf Name'}</Text>
        <Text style={styles.turfAddress}>{turf?.address || 'Turf Address'}</Text>
        <Text style={styles.turfCity}>
          {turf?.city || 'City'}, {turf?.country || 'Country'} {turf?.postalCode || ''}
        </Text>

        {/* Additional turf details */}
        <View style={styles.turfDetails}>
          {turf?.distance && (
            <View style={styles.turfDetailItem}>
              <Feather name="navigation" size={14} color="#00A693" />
              <Text style={styles.turfDetailText}>{turf.distance} km</Text>
            </View>
          )}
          {turf?.rating && (
            <View style={styles.turfDetailItem}>
              <Feather name="star" size={14} color="#00A693" />
              <Text style={styles.turfDetailText}>{turf.rating}</Text>
            </View>
          )}
        </View>

        {/* Book Now Button */}
        <TouchableOpacity
          style={styles.bookButton}
          onPress={() => {
            Alert.alert(
              'Booking',
              `You're about to book ${turf?.name || 'this turf'}. This feature is not implemented yet.`,
              [{ text: 'OK' }]
            );
          }}
        >
          <Text style={styles.bookButtonText}>Book Now</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingTop: 35,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333333',
    marginLeft: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666666',
  },
  mapContainer: {
    flex: 1,
  },
  map: {
    width: '100%',
    height: '100%',
  },
  fallbackContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
  },
  fallbackImage: {
    width: width * 0.5,
    height: width * 0.5,
    marginBottom: 20,
    opacity: 0.7,
  },
  fallbackText: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
  },
  turfInfoCard: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  turfName: {
    fontSize: 18,
    fontWeight: '700',
    color: '#333333',
    marginBottom: 8,
  },
  turfAddress: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  turfCity: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 12,
  },
  turfDetails: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  turfDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  turfDetailText: {
    fontSize: 14,
    color: '#00A693',
    fontWeight: '500',
    marginLeft: 4,
  },
  bookButton: {
    backgroundColor: '#00A693',
    borderRadius: 25,
    paddingVertical: 12,
    alignItems: 'center',
    marginTop: 8,
  },
  bookButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default MapScreen;
