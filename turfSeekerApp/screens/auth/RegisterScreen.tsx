import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';
import Checkbox from 'components/Checkbox';

const RegisterScreen = () => {
  const navigation = useNavigation();
  const [username, setUsername] = useState('');
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [password, setPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleSignup = () => {
    if (!termsAccepted) {
      Alert.alert('Terms Required', 'Please accept the terms and privacy policy to continue.');
      return;
    }

    // Validate inputs
    if (!username || !fullName || !email || !phoneNumber || !password) {
      Alert.alert('Missing Information', 'Please fill in all fields to continue.');
      return;
    }

    // Implement registration logic here
    console.log('Register with:', { username, fullName, email, phoneNumber, password });

    // Navigate to OTP verification screen
    // @ts-ignore - Type safety is handled at runtime
    navigation.navigate('OtpVerification', {
      phoneNumber,
      email,
      isRegistration: true
    });
  };

  const handleLogin = () => {
    // Navigate to login screen
    navigation.navigate('Login' as never);
  };

  const handleToggleTerms = (checked: boolean) => {
    setTermsAccepted(checked);
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // Social login handlers
  const handleAppleLogin = () => {
    console.log('Apple login pressed');
    Alert.alert('Apple Login', 'Apple login functionality will be implemented here.');
    // Implement Apple login functionality here
  };

  const handleGoogleLogin = () => {
    console.log('Google login pressed');
    Alert.alert('Google Login', 'Google login functionality will be implemented here.');
    // Implement Google login functionality here
  };

  const handleFacebookLogin = () => {
    console.log('Facebook login pressed');
    Alert.alert('Facebook Login', 'Facebook login functionality will be implemented here.');
    // Implement Facebook login functionality here
  };

  return (
    <SafeAreaView className="flex-1 bg-white pt-12">
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex-1"
      >
        <Animated.View
          className="flex-1 px-6 items-center justify-center"
          entering={FadeIn.duration(500)}
        >
          {/* Register Text */}
          <Text className="text-lg text-2xl font-bold text-gray-800 mt-8 text-center mb-2 ">Register</Text>
          <Text className="text-base text-gray-600 mt-2 mb-6 text-center">Create your account</Text>

          {/* Username Input */}
          <View className="w-full mb-3">
            <Text className="text-sm font-semibold text-gray-800 mb-2">Username</Text>
            <TextInput
              className="w-full h-12 border border-gray-300 rounded-lg px-4 text-base text-gray-800"
              placeholder="Enter your username"
              value={username}
              onChangeText={setUsername}
              autoCapitalize="none"
            />
          </View>

          {/* Full Name Input */}
          <View className="w-full mb-3">
            <Text className="text-sm font-semibold text-gray-800 mb-2">Full Name</Text>
            <TextInput
              className="w-full h-12 border border-gray-300 rounded-lg px-4 text-base text-gray-800"
              placeholder="Enter your full name"
              value={fullName}
              onChangeText={setFullName}
            />
          </View>

          {/* Email Input */}
          <View className="w-full mb-3">
            <Text className="text-sm font-semibold text-gray-800 mb-2">Email</Text>
            <TextInput
              className="w-full h-12 border border-gray-300 rounded-lg px-4 text-base text-gray-800"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          {/* Phone Number Input */}
          <View className="w-full mb-3">
            <Text className="text-sm font-semibold text-gray-800 mb-2">Phone Number</Text>
            <View className="flex-row w-full h-12 border border-gray-300 rounded-lg items-center">
              <Text className="px-4 text-base text-gray-800 border-r border-gray-300 pr-3">+880</Text>
              <TextInput
                className="flex-1 h-full px-4 text-base text-gray-800"
                placeholder="Enter your number"
                value={phoneNumber}
                onChangeText={setPhoneNumber}
                keyboardType="phone-pad"
              />
            </View>
          </View>

          {/* Password Input */}
          <View className="w-full mb-3">
            <Text className="text-sm font-semibold text-gray-800 mb-2">Password</Text>
            <View className="flex-row w-full h-12 border border-gray-300 rounded-lg items-center">
              <TextInput
                className="flex-1 h-full px-4 text-base text-gray-800"
                placeholder="Enter your password"
                value={password}
                onChangeText={setPassword}
                secureTextEntry={!showPassword}
              />
              <TouchableOpacity
                className="p-2"
                onPress={togglePasswordVisibility}
              >
                <Feather
                  name={showPassword ? 'eye' : 'eye-off'}
                  size={20}
                  color="#666"
                />
              </TouchableOpacity>
            </View>
          </View>

          {/* Terms and Privacy Policy */}
          <View className="flex-row items-start w-full my-3">
            <View className="mr-3 mt-1">
              <Checkbox
                checked={termsAccepted}
                size={20}
                color="#00A693"
                onToggle={handleToggleTerms}
              />
            </View>
            <Text className="flex-1 text-sm text-black leading-5 font-bold underline">
              By registering, You are agreeing to our Terms of use and privacy policy
            </Text>
          </View>


          {/* Signup Button */}
          <TouchableOpacity
            className={`w-full h-12 bg-teal-600 rounded-3xl justify-center items-center mb-4 ${
              !termsAccepted ? 'opacity-50' : ''
            }`}
            onPress={handleSignup}
            activeOpacity={0.8}
            disabled={!termsAccepted}
          >
            <Text className="text-base font-semibold text-white">Signup</Text>
          </TouchableOpacity>

          {/* OR Divider */}
          <View className="flex-row items-center w-full my-2">
            <View className="flex-1 h-px bg-gray-300" />
            <Text className="px-3 text-sm text-gray-600">OR</Text>
            <View className="flex-1 h-px bg-gray-300" />
          </View>

          {/* Social Login Options */}
          <View className="flex-row justify-center mb-4">
            <TouchableOpacity
              className="w-12 h-12 rounded-3xl justify-center items-center mx-2 bg-white shadow-sm"
              onPress={handleAppleLogin}
              activeOpacity={0.7}
            >
              <Image
                source={require('../../assets/apple-icon.png')}
                className="w-6 h-6"
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity
              className="w-12 h-12 rounded-3xl justify-center items-center mx-2 bg-white shadow-sm"
              onPress={handleGoogleLogin}
              activeOpacity={0.7}
            >
              <Image
                source={require('../../assets/google-icon.png')}
                className="w-6 h-6"
                resizeMode="contain"
              />
            </TouchableOpacity>
            <TouchableOpacity
              className="w-12 h-12 rounded-3xl justify-center items-center mx-2 bg-white shadow-sm"
              onPress={handleFacebookLogin}
              activeOpacity={0.7}
            >
              <Image
                source={require('../../assets/facebook-icon.png')}
                className="w-6 h-6"
                resizeMode="contain"
              />
            </TouchableOpacity>
          </View>

          {/* Login Option */}
          <View className="flex-row justify-center items-center mt-2">
            <Text className="text-sm text-gray-600">
              Already have an account?{' '}
            </Text>
            <TouchableOpacity onPress={handleLogin}>
              <Text className="text-base font-semibold text-teal-600 underline">Login</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );

};

export default RegisterScreen;
