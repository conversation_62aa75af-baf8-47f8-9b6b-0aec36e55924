import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import { Feather } from '@expo/vector-icons';

// Define the route params type
type OtpVerificationRouteParams = {
  email?: string;
  phoneNumber?: string;
  isRegistration?: boolean;
};

type OtpVerificationRouteProp = RouteProp<
  { OtpVerification: OtpVerificationRouteParams },
  'OtpVerification'
>;

const OtpVerificationScreen = () => {
  const navigation = useNavigation();
  const route = useRoute<OtpVerificationRouteProp>();
  const { email, phoneNumber, isRegistration } = route.params || { email: '', phoneNumber: '', isRegistration: false };

  const contactInfo = phoneNumber ? `+880 ${phoneNumber}` : email;
  const [otp, setOtp] = useState(['', '', '', '']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);

  const inputRefs = useRef<Array<TextInput | null>>([null, null, null, null]);

  // Timer for OTP resend
  useEffect(() => {
    if (timer > 0 && !canResend) {
      const interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
      return () => clearInterval(interval);
    } else if (timer === 0 && !canResend) {
      setCanResend(true);
    }
  }, [timer, canResend]);

  const handleOtpChange = (text: string, index: number) => {
    if (text.length > 1) {
      text = text[0]; // Only take the first character if multiple are pasted
    }

    // Update the OTP array
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    // Auto-focus next input if current input is filled
    if (text.length === 1 && index < 3) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // Move to previous input on backspace if current input is empty
    if (e.nativeEvent.key === 'Backspace' && index > 0 && otp[index] === '') {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOtp = () => {
    const otpValue = otp.join('');

    // Validate OTP
    if (otpValue.length !== 4) {
      Alert.alert('Invalid OTP', 'Please enter a valid 4-digit OTP.');
      return;
    }

    setIsSubmitting(true);

    // Simulate API call to verify OTP
    setTimeout(() => {
      setIsSubmitting(false);

      // For demo purposes, we'll consider "1234" as the correct OTP
      if (otpValue === '1234') {
        // Success case
        if (isRegistration) {
          // If coming from registration, show success and go to login
          Alert.alert(
            'Registration Successful',
            'Your account has been created successfully!',
            [
              {
                text: 'Login',
                onPress: () => {
                  // @ts-ignore - Type safety is handled at runtime
                  navigation.navigate('Login');
                },
              },
            ]
          );
        } else {
          // If coming from forgot password, go to reset password screen
          // @ts-ignore - Type safety is handled at runtime
          navigation.navigate('ResetPassword', { email });
        }
      } else {
        // Error case
        Alert.alert(
          'Invalid OTP',
          'The OTP you entered is incorrect. Please try again.',
          [
            {
              text: 'Try Again',
              onPress: () => {
                setOtp(['', '', '', '']);
                inputRefs.current[0]?.focus();
              },
            },
          ]
        );
      }
    }, 1500);
  };

  const handleResendOtp = () => {
    if (!canResend) return;

    // Reset timer and disable resend button
    setTimer(60);
    setCanResend(false);

    // Show confirmation
    Alert.alert('OTP Sent', `A new OTP has been sent to ${email}.`);
  };

  const handleBackToForgotPassword = () => {
    navigation.goBack();
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={styles.contentContainer}
            entering={FadeIn.duration(500)}
          >
            {/* Back Button */}
            <TouchableOpacity
              style={styles.backButton}
              onPress={handleBackToForgotPassword}
            >
              <Feather name="arrow-left" size={24} color="#333" />
            </TouchableOpacity>

            {/* Header Text */}
            <Text style={styles.headerText}>OTP Verification</Text>
            <Text style={styles.subtitle}>
              Enter OTP send to {contactInfo}
            </Text>

            {/* OTP Input */}
            <View style={styles.otpContainer}>
              {otp.map((digit, index) => (
                <TextInput
                  key={index}
                  ref={(ref) => (inputRefs.current[index] = ref)}
                  style={styles.otpInput}
                  value={digit}
                  onChangeText={(text) => handleOtpChange(text, index)}
                  onKeyPress={(e) => handleKeyPress(e, index)}
                  keyboardType="number-pad"
                  maxLength={1}
                  selectTextOnFocus
                />
              ))}
            </View>

            {/* Resend OTP */}
            <View style={styles.resendContainer}>
              <Text style={styles.resendText}>
                Dont receive the OTP ?
              </Text>
              <TouchableOpacity
                onPress={handleResendOtp}
                style={styles.resendButton}
              >
                <Text style={styles.resendButtonText}>
                  RESEND OTP
                </Text>
              </TouchableOpacity>
            </View>

            {/* Verify Button */}
            <TouchableOpacity
              style={styles.signupButton}
              onPress={handleVerifyOtp}
              activeOpacity={0.8}
              disabled={isSubmitting}
            >
              <Text style={styles.signupButtonText}>
                {isRegistration ? 'Signup' : 'Verify'}
              </Text>
            </TouchableOpacity>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingTop: 60,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 30,
  },
  contentContainer: {
    flex: 1,
    padding: 24,
    alignItems: 'center',
  },
  backButton: {
    position: 'absolute',
    top: 10,
    left: 20,
    zIndex: 10,
  },
  headerText: {
    fontSize: 24,
    fontWeight: '700',
    color: '#333333',
    marginTop: 60,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginTop: 8,
    marginBottom: 40,
    textAlign: 'center',
    paddingHorizontal: 20,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
    marginBottom: 30,
  },
  otpInput: {
    width: 50,
    height: 50,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    fontSize: 20,
    fontWeight: '600',
    textAlign: 'center',
  },
  resendContainer: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  resendText: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 10,
  },
  resendButton: {
    marginTop: 5,
  },
  resendButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#00A693',
  },
  signupButton: {
    width: '100%',
    height: 50,
    backgroundColor: '#00A693',
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
  },
  signupButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});

export default OtpVerificationScreen;
