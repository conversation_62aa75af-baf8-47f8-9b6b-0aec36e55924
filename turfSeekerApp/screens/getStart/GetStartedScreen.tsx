import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';

const { width } = Dimensions.get('window');

// Custom Checkbox Component
const CustomCheckbox = ({ checked = false , onToggle = () => {}, size = 20 }) => {
  return (
    <TouchableOpacity
      className={`w-5 h-5 border-2 rounded-sm justify-center items-center ${
        checked 
          ? 'bg-teal-600 border-teal-600' 
          : 'bg-white border-gray-300'
      }`}
      onPress={onToggle}
      activeOpacity={0.8}
    >
      {checked && (
        <Text className="text-white text-xs font-bold">✓</Text>
      )}
    </TouchableOpacity>
  );
};

const GetStartedScreen = () => {
  const navigation = useNavigation();
  const [termsAccepted, setTermsAccepted] = useState(true);

  const handleLogin = () => {
    if (!termsAccepted) {
      Alert.alert('Terms Required', 'Please accept the terms and privacy policy to continue.');
      return;
    }
    console.log('Navigate to login screen');
    navigation.navigate('Login' as never);
  };

  const handleRegister = () => {
    if (!termsAccepted) {
      Alert.alert('Terms Required', 'Please accept the terms and privacy policy to continue.');
      return;
    }
    console.log('Navigate to register screen');
    navigation.navigate('Register' as never);
  };

  const handleContinueWithoutAccount = () => {
    if (!termsAccepted) {
      Alert.alert('Terms Required', 'Please accept the terms and privacy policy to continue.');
      return;
    }
    console.log('Continue without account');
  };

  const handleToggleTerms = () => {
    setTermsAccepted(!termsAccepted);
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <StatusBar style="dark" />

      <ScrollView
        contentContainerStyle={{ flexGrow: 1, alignItems: 'center', paddingBottom: 30 }}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View
          className="flex-1 items-center w-full px-8 pt-36"
          entering={FadeIn.duration(500)}
        >
          {/* Logo */}
          <Image
            source={require('../../assets/Logo.png')}
            className="mb-10"
            style={{ width: width * 0.4, height: width * 0.3 }}
            resizeMode="contain"
          />

          {/* Title and Subtitle */}
          <View className="mb-5">
            <Text className="text-base text-gray-800 text-center leading-7">
              <Text className="text-3xl font-bold text-teal-600">Get started</Text>
              {` now by either registering a new account, or logging into an existing one.`}
            </Text>
          </View>

          {/* Or text */}
          <Text className="text-lg font-semibold text-teal-600 text-center my-4">Or</Text>

          {/* Subtitle 2 */}
          <Text className="text-base text-gray-800 text-center mb-8">
            You can use the app without an account.
          </Text>

          {/* Login Button */}
          <TouchableOpacity
            className="w-full h-12 bg-teal-600 rounded-full justify-center items-center mb-4 shadow-md"
            onPress={handleLogin}
            activeOpacity={0.8}
          >
            <Text className="text-base font-semibold text-white">Login</Text>
          </TouchableOpacity>

          {/* Register Button */}
          <TouchableOpacity
            className="w-full h-12 bg-teal-600 rounded-full justify-center items-center mb-4 shadow-md"
            onPress={handleRegister}
            activeOpacity={0.8}
          >
            <Text className="text-base font-semibold text-white">Register</Text>
          </TouchableOpacity>

          {/* Continue Without Account Button */}
          <TouchableOpacity
            className="w-full h-12 bg-white rounded-full justify-center items-center mb-8 border border-gray-300 shadow-sm"
            onPress={handleContinueWithoutAccount}
            activeOpacity={0.8}
          >
            <Text className="text-base font-semibold text-gray-800">Continue without an account</Text>
          </TouchableOpacity>

          {/* Terms and Privacy Policy */}
          <View className="flex-row items-start mt-2 px-2">
            <View className="mr-3 mt-1">
              <CustomCheckbox
                checked={termsAccepted}
                onToggle={handleToggleTerms}
                size={20}
              />
            </View>
            <Text className="flex-1 text-sm text-black leading-5 font-bold underline">
              By registering, You are agreeing to our Terms of use and privacy policy
            </Text>
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default GetStartedScreen;