import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  SafeAreaView,
  ScrollView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Animated, { FadeIn } from 'react-native-reanimated';
import { StatusBar } from 'expo-status-bar';
import { GestureDetector, Gesture } from 'react-native-gesture-handler';

import PaginationDots from '../../components/PaginationDots';

const { width } = Dimensions.get('window');

const ThirdScreen = () => {
  const navigation = useNavigation();
  const currentPage = 2; // This is the third screen (0-indexed)
  const totalPages = 4;

  const handleSkip = () => {
    // Navigate to the next screen
    navigation.navigate('FourthScreen' as never);
  };

  const handleContinue = () => {
    // Navigate to the next onboarding screen
    navigation.navigate('FourthScreen' as never);
  };

  const handleDotPress = (index: number) => {
    // Navigate to the selected screen based on dot index
    if (index === 0) navigation.navigate('FirstScreen' as never);
    if (index === 1) navigation.navigate('SecondScreen' as never);
    if (index === 2) return; // Already on third screen
    if (index === 3) navigation.navigate('FourthScreen' as never);
  };

  // Swipe gesture handling
  const swipeGesture = Gesture.Pan()
    .onEnd((event) => {
      if (event.translationX < -50) {
        
        navigation.navigate('FourthScreen' as never);
      } else if (event.translationX > 50) {
       
        navigation.navigate('SecondScreen' as never);
      }
    });

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />

     
      <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
        <Text style={styles.skipText}>Skip</Text>
      </TouchableOpacity>

      <GestureDetector gesture={swipeGesture}>
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          <Animated.View
            style={styles.contentContainer}
            entering={FadeIn.duration(500)}
          >
        
          <Image
           source={require('../../assets/Hand.png')}
            style={styles.illustration}
            resizeMode="contain"
          />

       
          <View style={styles.textContainer}>
            <Text style={styles.title}>Easy Cancellation & Refund</Text>
            <Text style={styles.description}>
              Plans changed? No problem! Easily cancel your bookings and get quick refunds with our flexible cancellation policy giving you peace of mind.
            </Text>
          </View>

          {/* Pagination dots */}
          <PaginationDots
            currentPage={currentPage}
            totalPages={totalPages}
            onDotPress={handleDotPress}
          />

          {/* Action button */}
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleContinue}
            activeOpacity={0.8}
          >
            <Text style={styles.actionButtonText}>What's Next</Text>
          </TouchableOpacity>
        </Animated.View>
        </ScrollView>
      </GestureDetector>

      
      {/* <View style={styles.bottomIndicator} /> */}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    alignItems: 'center',
    paddingBottom: 30,
  },
  contentContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    width: width,
    paddingHorizontal: 30,
  },
  skipButton: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '600',
    textDecorationLine: 'underline',
    color: '#000000',
  },
  illustration: {
    width: width * 0.7,
    height: width * 0.7,
    marginBottom: 40,
    marginTop: 60,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 16,
    textAlign: 'center',
    color: '#333333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
    color: '#666666',
  },
  actionButton: {
    width: '100%',
    backgroundColor: '#00A693',
    paddingVertical: 16,
    borderRadius: 30,
    alignItems: 'center',
    marginTop: 30,
    marginBottom: 20,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  bottomIndicator: {
    width: 40,
    height: 4,
    backgroundColor: '#D9D9D9',
    borderRadius: 2,
    alignSelf: 'center',
    marginBottom: 10,
  },
});

export default ThirdScreen;