import React, { useEffect } from 'react';
import { View, Text, Image, TouchableOpacity } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withDelay,
  Easing
} from 'react-native-reanimated';


const WelcomeScreen = () => {
  const navigation = useNavigation();
  // Initial animation values
  const translateY = useSharedValue(100);
  const opacity = useSharedValue(0);
  const buttonScale = useSharedValue(0.5);

  useEffect(() => {
    translateY.value = withTiming(0, {
      duration: 800,
      easing: Easing.out(Easing.cubic),
    });
    // Fade in the content
    opacity.value = withDelay(300, withTiming(1, {
      duration: 600,
      easing: Easing.out(Easing.cubic),
    }));

    buttonScale.value = withDelay(600, withTiming(1, {
      duration: 500,
      easing: Easing.elastic(1.2),
    }));
  }, []);

  const animatedContainerStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: translateY.value }],
    };
  });

  const contentStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  const buttonStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: buttonScale.value }],
      opacity: opacity.value,
    };
  });

  const handleButtonPress = () => {
    navigation.navigate('FirstScreen' as never);
  };

  return (
    <View className="flex-1 bg-gray-100">
      {/* Background Image */}
      <Image
        source={require('../../assets/54164-O816UD 1.png')}
        className="absolute top-0 left-0 w-[110%] h-[85%]"
        resizeMode="cover"
      />

      {/* Logo Container - Upper portion */}
      <Animated.View style={contentStyle} className="flex-1 justify-center items-center px-8 pt-16">
        <Image
          source={require('../../assets/Logo.png')}
          className="w-64 h-48"
          resizeMode="contain"
        />
      </Animated.View>

      {/* Main Content Container - Bottom portion */}
      <Animated.View
        style={[animatedContainerStyle]}
        className="bg-[#009688] rounded-t-[32px] px-12 py-28"
      >
        <Animated.View style={contentStyle} className="items-center">
          {/* Main Title */}
          <Text className="font-bold text-3xl text-white text-center mb-3">
            Claim Your Game Space
          </Text>

          {/* Subtitle */}
          <Text className="font-normal text-lg text-white text-center mb-16">
            Your play, your turf, your app
          </Text>

          {/* Button */}
          <Animated.View style={buttonStyle} className="mb-[40px]">
            <TouchableOpacity
              onPress={handleButtonPress}
              className="bg-white rounded-full px-8 py-4 min-w-[280px] items-center"
            >
              <Text className="font-semibold text-lg text-gray-700">
                Hey! What is Turf Seeker?
              </Text>
            </TouchableOpacity>
          </Animated.View>

        </Animated.View>
      </Animated.View>
    </View>
  );
};

export default WelcomeScreen;