import React, { useEffect } from 'react';
import { View, Image, Dimensions, TouchableOpacity, StatusBar } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  runOnJS
} from 'react-native-reanimated';

const { height } = Dimensions.get('window');

const SplashScreen = () => {
  const navigation = useNavigation();
  const translateY = useSharedValue(0);

  const navigateToWelcome = () => {
    navigation.navigate('Welcome' as never);
  };

  const handlePress = () => {
    // Animate the splash image upward on press
    translateY.value = withTiming(-height, { duration: 1000 }, (finished) => {
      if (finished) {
        runOnJS(navigateToWelcome)();
      }
    });
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  // Hide the status bar to achieve full screen effect
  useEffect(() => {
    StatusBar.setHidden(true);
    return () => {
      StatusBar.setHidden(false);
    };
  }, []);

  return (
    <View style={{ flex: 1, width: '100%', height: '100%' }}>
      <StatusBar hidden={true} />
      <TouchableOpacity 
        activeOpacity={1} 
        onPress={handlePress}
        style={{ flex: 1, width: '100%', height: '100%' }}
      >
        <Animated.View 
          style={[
            animatedStyle, 
            { position: 'absolute', width: '100%', height: '100%' }
          ]}
        >
          <Image
            source={require('../../assets/splash.png')}
            style={{ width: '100%', height: '100%' }}
            resizeMode="cover"
          />
        </Animated.View>
      </TouchableOpacity>
    </View>
  );
};

export default SplashScreen;