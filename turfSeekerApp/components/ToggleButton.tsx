import React from 'react';
import { View, TouchableOpacity, StyleSheet, Animated } from 'react-native';

interface ToggleButtonProps {
  isEnabled: boolean;
  onToggle: (value: boolean) => void;
  size?: 'small' | 'medium' | 'large';
  activeColor?: string;
  inactiveColor?: string;
  thumbColor?: string;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({
  isEnabled,
  onToggle,
  size = 'medium',
  activeColor = '#00A693',
  inactiveColor = '#E0E0E0',
  thumbColor = '#FFFFFF',
}) => {
  // Determine dimensions based on size
  const dimensions = {
    small: { width: 36, height: 20, thumbSize: 16 },
    medium: { width: 44, height: 24, thumbSize: 20 },
    large: { width: 52, height: 28, thumbSize: 24 },
  };

  const { width, height, thumbSize } = dimensions[size];
  
  // Calculate the position of the thumb
  const thumbPosition = isEnabled ? width - thumbSize - 2 : 2;

  const handlePress = () => {
    onToggle(!isEnabled);
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={handlePress}
    >
      <View
        style={[
          styles.track,
          {
            width,
            height,
            backgroundColor: isEnabled ? activeColor : inactiveColor,
            borderRadius: height / 2,
          },
        ]}
      >
        <View
          style={[
            styles.thumb,
            {
              width: thumbSize,
              height: thumbSize,
              backgroundColor: thumbColor,
              transform: [{ translateX: thumbPosition }],
            },
          ]}
        />
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  track: {
    justifyContent: 'center',
  },
  thumb: {
    borderRadius: 999,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
});

export default ToggleButton;
