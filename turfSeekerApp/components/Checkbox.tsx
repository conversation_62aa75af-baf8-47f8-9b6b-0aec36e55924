import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface CheckboxProps {
  checked?: boolean;
  size?: number;
  color?: string;
  onToggle?: (checked: boolean) => void;
}

const Checkbox: React.FC<CheckboxProps> = ({
  checked = true,
  size = 20,
  color = '#00A693',
  onToggle,
}) => {
  const handlePress = () => {
    if (onToggle) {
      onToggle(!checked);
    }
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View
        style={{
          width: size,
          height: size,
          borderRadius: 4,
          borderWidth: 1,
          borderColor: color,
          backgroundColor: checked ? color : 'transparent',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        {checked && (
          <Svg width={size * 0.7} height={size * 0.7} viewBox="0 0 24 24" fill="none">
            <Path
              d="M5 12L10 17L19 8"
              stroke="white"
              strokeWidth="2.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </Svg>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default Checkbox;
