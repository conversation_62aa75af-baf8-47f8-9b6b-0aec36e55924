import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';

interface PaginationDotsProps {
  currentPage: number;
  totalPages: number;
  onDotPress?: (index: number) => void;
}

const PaginationDots = ({
  currentPage,
  totalPages,
  onDotPress
}: PaginationDotsProps) => {
  return (
    <View style={styles.container}>
      {Array.from({ length: totalPages }).map((_, index) => (
        <TouchableOpacity
          key={index}
          onPress={() => onDotPress && onDotPress(index)}
          activeOpacity={0.7}
        >
          <Dot
            isActive={index === currentPage}
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

interface DotProps {
  isActive: boolean;
}

const Dot = ({ isActive }: DotProps) => {
  const animatedStyle = useAnimatedStyle(() => {
    return {
      width: withTiming(isActive ? 24 : 8, { duration: 300 }),
      backgroundColor: withTiming(
        isActive ? '#00A693' : '#D9D9D9',
        { duration: 300 }
      ),
    };
  });

  return (
    <Animated.View
      style={[
        styles.dot,
        animatedStyle,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 20,
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default PaginationDots;