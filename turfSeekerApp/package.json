{"name": "my-expo-app", "version": "1.0.0", "scripts": {"android": "expo start --android", "ios": "expo start --ios", "start": "expo start", "prebuild": "expo prebuild", "lint": "eslint \"**/*.{js,jsx,ts,tsx}\" && prettier -c \"**/*.{js,jsx,ts,tsx,json}\"", "format": "eslint \"**/*.{js,jsx,ts,tsx}\" --fix && prettier \"**/*.{js,jsx,ts,tsx,json}\" --write", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "expo": "^53.0.9", "expo-build-properties": "^0.14.6", "expo-constants": "~17.1.6", "expo-image": "~2.1.7", "expo-location": "~18.1.5", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "install": "^0.13.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "^1.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.12.0", "react-native-webview": "^13.13.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "eslint": "^9.25.1", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^10.1.2", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.17", "typescript": "~5.8.3"}, "main": "node_modules/expo/AppEntry.js", "private": true}