{"name": "turfseekerserver", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start:prod": "node ./dist/server.js", "start:dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "http-status": "^2.1.0", "mongoose": "^8.15.0", "nodemailer": "^7.0.3", "zod": "^3.25.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.18", "@types/express": "^5.0.2", "@types/mongoose": "^5.11.96", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}