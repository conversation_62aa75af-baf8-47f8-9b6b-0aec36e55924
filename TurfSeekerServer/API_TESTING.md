# API Testing Guide

## User Registration with <PERSON>ail OTP

### Step 1: Start Registration (Sends OTP to Email)

**POST** `/api/v1/users/register`

```json
{
  "username": "testuser",
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567890"
}
```

**Expected Response:**
```json
{
  "success": true,
  "statusCode": 200,
  "message": "O<PERSON> sent to your email address. Please verify to complete registration.",
  "data": null
}
```

**What happens:** System automatically sends <PERSON><PERSON> to the provided email address.

### Step 2: Complete Registration with OTP

**POST** `/api/v1/users/verify-registration`

```json
{
  "username": "testuser",
  "fullName": "Test User",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+1234567890",
  "otp": "123456"
}
```

**Expected Response:**
```json
{
  "success": true,
  "statusCode": 201,
  "message": "User registered successfully",
  "data": {
    "username": "testuser",
    "fullName": "Test User",
    "email": "<EMAIL>",
    "phoneNumber": "+1234567890",
    "isEmailVerified": true,
    "_id": "...",
    "createdAt": "...",
    "updatedAt": "..."
  }
}
```

## Alternative: Manual OTP Operations (Optional)

If you still want to manually send/verify OTP:

### Send OTP to Email

**POST** `/api/v1/otp/send`

```json
{
  "email": "<EMAIL>"
}
```

### Verify OTP

**POST** `/api/v1/otp/verify`

```json
{
  "email": "<EMAIL>",
  "otp": "123456"
}
```

## Environment Setup

Make sure to set up your `.env` file with email configuration:

```env
NODE_ENV=development
PORT=3000
DATABASE_URL=your_mongodb_connection_string
JWT_SECRET=mysecretkey
JWT_EXPIRES_IN=1d
JWT_COOKIE_EXPIRES_IN=1

# Email Configuration for OTP
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_PASS=your_app_password
EMAIL_FROM=<EMAIL>
```

## Testing with Postman or curl

### Step 1: Start Registration (Sends OTP)
```bash
curl -X POST http://localhost:3000/api/v1/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "fullName": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "phoneNumber": "+1234567890"
  }'
```

### Step 2: Complete Registration with OTP
```bash
curl -X POST http://localhost:3000/api/v1/users/verify-registration \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "fullName": "Test User",
    "email": "<EMAIL>",
    "password": "password123",
    "phoneNumber": "+1234567890",
    "otp": "123456"
  }'
```

### Alternative: Manual OTP Operations

#### Send OTP
```bash
curl -X POST http://localhost:3000/api/v1/otp/send \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

#### Verify OTP
```bash
curl -X POST http://localhost:3000/api/v1/otp/verify \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "otp": "123456"}'
```
