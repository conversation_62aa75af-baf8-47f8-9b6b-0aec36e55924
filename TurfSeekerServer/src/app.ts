import express, { Application, Request, Response } from "express";
import cookieParser from "cookie-parser";
import cors from "cors";
import helmet from "helmet";
import globalErrorHandler from "./middlewares/globalErrorHandler";
import notFound from "./middlewares/notFound";
import router from "./routes";

const app: Application = express();
//security middleware
app.use(helmet());
//parsers
app.use(express.json());
app.use(cookieParser());
app.use(cors({ origin: ["http://localhost:5173"], credentials: true }));

// application routes
app.use("/api/v1", router);

// rate limit
// const limiter = rateLimit({
// 	windowMs: 15 * 60 * 1000,
// 	limit: 100,
// 	message: "Too many requests",
// });

// app.use(limiter);

app.get("/", (req: Request, res: Response) => {
	res.send("Hi! This is the API for the Turf Booking System");
});

app.use(globalErrorHandler);

//Not Found
app.use(notFound);

export default app;
