import { Request, Response, NextFunction, RequestHandler } from "express";
import httpStatus from "http-status";

export const notFound: RequestHandler = (
	req: Request,
	res: Response,
	next: NextFunction
): void => {
	res.status(httpStatus.NOT_FOUND).json({
		success: false,
		statusCode: httpStatus.NOT_FOUND,
		message: "Api endpoint not found",
		error: {
			path: req.originalUrl,
			method: req.method,
		},
	});
};

export default notFound;
