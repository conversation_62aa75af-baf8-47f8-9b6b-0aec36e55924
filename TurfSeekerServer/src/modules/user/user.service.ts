import { IUser } from "./user.interface";
import { User } from "./user.model";
import httpStatus from "http-status";
import AppError from "../../errors/AppError";
import { OTPService } from "../otp/otp.service";

// Step 1: Initial registration - sends OTP to email
const registerUserIntoDB = async (userData: IUser): Promise<{ message: string }> => {
	// Check if user already exists
	const existingUser = await User.findOne({
		$or: [
			{ email: userData.email },
			{ username: userData.username },
			{ phoneNumber: userData.phoneNumber },
		],
	});

	if (existingUser) {
		throw new AppError(
			httpStatus.BAD_REQUEST,
			"User already exists with this email, username, or phone number"
		);
	}

	// Send OTP to the registration email
	await OTPService.sendOTPToEmail(userData.email);

	return {
		message: "OTP sent to your email address. Please verify to complete registration.",
	};
};

// Step 2: Verify OTP and complete registration
const verifyAndCompleteRegistration = async (userData: IUser & { otp: string }): Promise<IUser> => {
	const { otp, ...userDataWithoutOTP } = userData;

	// Verify OTP first
	await OTPService.verifyOTP({
		email: userData.email,
		otp: otp,
	});

	// Check if user already exists (double-check)
	const existingUser = await User.findOne({
		$or: [
			{ email: userData.email },
			{ username: userData.username },
			{ phoneNumber: userData.phoneNumber },
		],
	});

	if (existingUser) {
		throw new AppError(
			httpStatus.BAD_REQUEST,
			"User already exists with this email, username, or phone number"
		);
	}

	// Create user with verified email
	const user = await User.create({
		...userDataWithoutOTP,
		isEmailVerified: true,
	});

	// Clean up the verified OTP
	await OTPService.cleanupVerifiedOTP(userData.email);

	return user;
};

const getAllUsersFromDB = async () => {
	const users = await User.find({}).select("-password");
	return users;
};

const getUserByIdFromDB = async (id: string) => {
	const user = await User.findById(id).select("-password");
	if (!user) {
		throw new AppError(httpStatus.NOT_FOUND, "User not found");
	}
	return user;
};

export const UserService = {
	registerUserIntoDB,
	verifyAndCompleteRegistration,
	getAllUsersFromDB,
	getUserByIdFromDB,
};
