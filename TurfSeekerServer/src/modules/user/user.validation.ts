import { z } from "zod";

const registerUser = z.object({
	body: z.object({
		username: z.string().min(3).max(30),
		fullName: z.string().min(3).max(100),
		email: z.string().email(),
		password: z.string().min(6),
		phoneNumber: z.string().min(10).max(15),
	}),
});

const verifyAndCompleteRegistration = z.object({
	body: z.object({
		username: z.string().min(3).max(30),
		fullName: z.string().min(3).max(100),
		email: z.string().email(),
		password: z.string().min(6),
		phoneNumber: z.string().min(10).max(15),
		otp: z.string().length(6),
	}),
});

export const userValidation = {
	registerUser,
	verifyAndCompleteRegistration,
};
