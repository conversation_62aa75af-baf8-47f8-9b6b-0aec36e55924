import { Router } from "express";
import { UserController } from "./user.controller";
import validateRequest from "../../middlewares/validateRequest";
import { userValidation } from "./user.validation";

const router = Router();

router.post(
	"/register",
	validateRequest(userValidation.registerUser),
	UserController.registerUser
);

router.post(
	"/verify-registration",
	validateRequest(userValidation.verifyAndCompleteRegistration),
	UserController.verifyAndCompleteRegistration
);

router.get("/", UserController.getAllUsers);
router.get("/:id", UserController.getUserById);

export const UserRoutes = router;
