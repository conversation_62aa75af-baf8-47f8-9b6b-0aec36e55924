import { Request, Response } from "express";
import { UserService } from "./user.service";

import httpStatus from "http-status";
import sendResponse from "../../utils/sendResponse";
import catchAsync from "../../utils/catchAsync";

const registerUser = catchAsync(async (req: Request, res: Response) => {
	const result = await UserService.registerUserIntoDB(req.body);
	sendResponse(res, {
		success: true,
		statusCode: httpStatus.OK,
		message: result.message,
		data: null,
	});
});

const verifyAndCompleteRegistration = catchAsync(async (req: Request, res: Response) => {
	const result = await UserService.verifyAndCompleteRegistration(req.body);
	sendResponse(res, {
		success: true,
		statusCode: httpStatus.CREATED,
		message: "User registered successfully",
		data: result,
	});
});

const getAllUsers = catchAsync(async (req: Request, res: Response) => {
	const result = await UserService.getAllUsersFromDB();
	sendResponse(res, {
		success: true,
		statusCode: httpStatus.OK,
		message: "Users retrieved successfully",
		data: result,
	});
});

const getUserById = catchAsync(async (req: Request, res: Response) => {
	const result = await UserService.getUserByIdFromDB(req.params.id);
	sendResponse(res, {
		success: true,
		statusCode: httpStatus.OK,
		message: "User retrieved successfully",
		data: result,
	});
});

export const UserController = {
	registerUser,
	verifyAndCompleteRegistration,
	getAllUsers,
	getUserById,
};
