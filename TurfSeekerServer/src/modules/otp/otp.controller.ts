import { Request, Response } from "express";
import { OTPService } from "./otp.service";
import httpStatus from "http-status";
import sendResponse from "../../utils/sendResponse";
import catchAsync from "../../utils/catchAsync";

const sendOTP = catchAsync(async (req: Request, res: Response) => {
	const { email } = req.body;
	const result = await OTPService.sendOTPToEmail(email);

	sendResponse(res, {
		success: true,
		statusCode: httpStatus.OK,
		message: result.message,
		data: null,
	});
});

const verifyOTP = catchAsync(async (req: Request, res: Response) => {
	const result = await OTPService.verifyOTP(req.body);

	sendResponse(res, {
		success: true,
		statusCode: httpStatus.OK,
		message: result.message,
		data: { isValid: result.isValid },
	});
});

export const OTPController = {
	sendOTP,
	verifyOTP,
};
