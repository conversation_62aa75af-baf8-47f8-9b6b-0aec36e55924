import mongoose, { Schema } from "mongoose";
import { IOTP } from "./otp.interface";

const otpSchema = new Schema<IOTP>(
	{
		email: {
			type: String,
			required: true,
			trim: true,
			lowercase: true,
		},
		otp: {
			type: String,
			required: true,
		},
		expiresAt: {
			type: Date,
			required: true,
			index: { expireAfterSeconds: 0 }, // MongoDB TTL index for automatic deletion
		},
		isVerified: {
			type: Boolean,
			default: false,
		},
		attempts: {
			type: Number,
			default: 0,
			max: 3, // Maximum 3 attempts allowed
		},
	},
	{
		timestamps: true,
	}
);

// Index for faster queries
otpSchema.index({ email: 1, createdAt: -1 });

export const OTP = mongoose.model<IOTP>("OTP", otpSchema);
