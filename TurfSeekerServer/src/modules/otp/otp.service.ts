import nodemailer from "nodemailer";
import config from "../../config";
import { IOTP, IOTPVerification } from "./otp.interface";
import { OTP } from "./otp.model";
import AppError from "../../errors/AppError";
import httpStatus from "http-status";

// Initialize Nodemailer transporter
const transporter = nodemailer.createTransport({
	host: config.email.host,
	port: config.email.port,
	secure: false, // true for 465, false for other ports
	auth: {
		user: config.email.from,
		pass: config.email.pass,
	},
});

// Generate 6-digit OTP
const generateOTP = (): string => {
	return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send OTP via Email using Nodemailer
const sendOTPEmail = async (email: string, otp: string): Promise<void> => {
	try {
		const mailOptions = {
			from: config.email.from,
			to: email,
			subject: "TurfSeeker - Email Verification Code",
			html: `
				<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
					<h2 style="color: #333;">TurfSeeker Email Verification</h2>
					<p>Your verification code is:</p>
					<div style="background-color: #f4f4f4; padding: 20px; text-align: center; margin: 20px 0;">
						<h1 style="color: #007bff; font-size: 32px; margin: 0; letter-spacing: 5px;">${otp}</h1>
					</div>
					<p>This code will expire in <strong>10 minutes</strong>.</p>
					<p>If you didn't request this code, please ignore this email.</p>
					<hr style="margin: 30px 0;">
					<p style="color: #666; font-size: 12px;">This is an automated email from TurfSeeker. Please do not reply.</p>
				</div>
			`,
		};

		await transporter.sendMail(mailOptions);
	} catch (error) {
		console.error("Email sending error:", error);
		throw new AppError(
			httpStatus.INTERNAL_SERVER_ERROR,
			"Failed to send OTP email. Please try again."
		);
	}
};

// Send OTP to email
const sendOTPToEmail = async (email: string): Promise<{ message: string }> => {
	// Check if there's a recent OTP request (rate limiting)
	const recentOTP = await OTP.findOne({
		email,
		createdAt: { $gte: new Date(Date.now() - 2 * 60 * 1000) }, // 2 minutes
	});

	if (recentOTP) {
		throw new AppError(
			httpStatus.TOO_MANY_REQUESTS,
			"Please wait 2 minutes before requesting another OTP"
		);
	}

	// Generate new OTP
	const otp = generateOTP();
	const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now

	// Delete any existing OTPs for this email
	await OTP.deleteMany({ email });

	// Create new OTP record
	await OTP.create({
		email,
		otp,
		expiresAt,
		isVerified: false,
		attempts: 0,
	});

	// Send OTP via Email
	await sendOTPEmail(email, otp);

	return {
		message: "OTP sent successfully to your email address",
	};
};

// Verify OTP
const verifyOTP = async (data: IOTPVerification): Promise<{ message: string; isValid: boolean }> => {
	const { email, otp } = data;

	// Find the OTP record
	const otpRecord = await OTP.findOne({
		email,
		isVerified: false,
	}).sort({ createdAt: -1 });

	if (!otpRecord) {
		throw new AppError(
			httpStatus.NOT_FOUND,
			"No OTP found for this email address or OTP already verified"
		);
	}

	// Check if OTP has expired
	if (otpRecord.expiresAt < new Date()) {
		await OTP.deleteOne({ _id: otpRecord._id });
		throw new AppError(httpStatus.BAD_REQUEST, "OTP has expired. Please request a new one");
	}

	// Check attempts limit
	if (otpRecord.attempts >= 3) {
		await OTP.deleteOne({ _id: otpRecord._id });
		throw new AppError(
			httpStatus.BAD_REQUEST,
			"Maximum verification attempts exceeded. Please request a new OTP"
		);
	}

	// Increment attempts
	otpRecord.attempts += 1;
	await otpRecord.save();

	// Verify OTP
	if (otpRecord.otp !== otp) {
		throw new AppError(
			httpStatus.BAD_REQUEST,
			`Invalid OTP. ${3 - otpRecord.attempts} attempts remaining`
		);
	}

	// Mark OTP as verified
	otpRecord.isVerified = true;
	await otpRecord.save();

	return {
		message: "OTP verified successfully",
		isValid: true,
	};
};

// Check if email has verified OTP
const isEmailVerified = async (email: string): Promise<boolean> => {
	const verifiedOTP = await OTP.findOne({
		email,
		isVerified: true,
		expiresAt: { $gte: new Date() },
	});

	return !!verifiedOTP;
};

// Clean up verified OTP after successful registration
const cleanupVerifiedOTP = async (email: string): Promise<void> => {
	await OTP.deleteMany({ email, isVerified: true });
};

export const OTPService = {
	sendOTPToEmail,
	verifyOTP,
	isEmailVerified,
	cleanupVerifiedOTP,
};
