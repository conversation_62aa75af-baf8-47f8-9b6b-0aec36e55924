import { z } from "zod";

const sendOTP = z.object({
	body: z.object({
		email: z.string().email(),
	}),
});

const verifyOTP = z.object({
	body: z.object({
		email: z.string().email(),
		otp: z.string().length(6),
	}),
});

const registerWithOTP = z.object({
	body: z.object({
		username: z.string().min(3).max(30),
		fullName: z.string().min(3).max(100),
		email: z.string().email(),
		password: z.string().min(6),
		phoneNumber: z.string().min(10).max(15),
		otp: z.string().length(6),
	}),
});

export const otpValidation = {
	sendOTP,
	verifyOTP,
	registerWithOTP,
};
