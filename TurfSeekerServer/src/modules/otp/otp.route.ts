import { Router } from "express";
import { OTPController } from "./otp.controller";
import validateRequest from "../../middlewares/validateRequest";
import { otpValidation } from "./otp.validation";

const router = Router();

router.post(
	"/send",
	validateRequest(otpValidation.sendOTP),
	OTPController.sendOTP
);

router.post(
	"/verify",
	validateRequest(otpValidation.verifyOTP),
	OTPController.verifyOTP
);

export const OTPRoutes = router;
