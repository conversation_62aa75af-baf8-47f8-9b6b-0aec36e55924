import { Router } from "express";
import { UserRoutes } from "../modules/user/user.route";
import { OTPRoutes } from "../modules/otp/otp.route";

const router = Router();

// Define all routes in an array of objects
const moduleRoutes = [
	{
		path: "/users",
		route: UserRoutes,
	},
	{
		path: "/otp",
		route: OTPRoutes,
	},
	// Add more routes here in the future
	// Example:
	// {
	//   path: '/auth',
	//   route: AuthRoutes,
	// },
	// {
	//   path: '/turf',
	//   route: TurfRoutes,
	// },
];

// Apply all routes using forEach
moduleRoutes.forEach((route) => router.use(route.path, route.route));

export default router;
